#!/bin/sh /etc/rc.common
# (C) 2013 openwrt.org

START=15

USE_PROCD=1
NAME="led_control"
PROG="/usr/bin/led_control"

reload_service() {
    stop
    start
}

service_triggers() {
    procd_add_reload_trigger "led_control"
    procd_open_validate
    procd_close_validate
}

start_service() {
	local enable

	procd_open_instance
	procd_set_param command "$PROG"
	procd_set_param respawn
	procd_set_param stderr 1
	procd_set_param stdout 1
	procd_close_instance
}

