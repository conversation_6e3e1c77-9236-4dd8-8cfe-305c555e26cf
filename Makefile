include $(TOPDIR)/rules.mk

PKG_NAME := led_control
PKG_VERSION := 1.0
PKG_RELEASE := 1
PKG_BUILD_DIR := $(BUILD_DIR)/$(PKG_NAME)

include $(INCLUDE_DIR)/package.mk

define Package/$(PKG_NAME)
	CATEGORY := Utilities
	TITLE := VSOL led control
	DEPENDS := +libblobmsg-json +libjson-c +libubus +libubox +libuci +vsuci
endef

define Package/$(PKG_NAME)/description
	VSOL led control
endef


define Build/Compile
$(MAKE) -C $(PKG_BUILD_DIR) \
	CC="$(TARGET_CC)" \
	CFLAGS="$(TARGET_CFLAGS) $(SMART_QOS_CONFIGS) -g -flto -Wall" \
	LDFLAGS="$(TARGET_LDFLAGS)"
endef

define Package/$(PKG_NAME)/install
	$(INSTALL_DIR) $(1)/etc/config
	$(INSTALL_DIR) $(1)/etc/init.d
	$(INSTALL_DIR) $(1)/usr/bin
	$(INSTALL_BIN) ./files/led_control $(1)/etc/init.d/led_control
	$(INSTALL_BIN) $(PKG_BUILD_DIR)/led_control $(1)/usr/bin/
endef

$(eval $(call BuildPackage,$(PKG_NAME)))
