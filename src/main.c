#include "led_util.h"

// 全局变量用于线程间通信
static pthread_t wps_timer_thread;
static pthread_mutex_t wps_timer_mutex = PTHREAD_MUTEX_INITIALIZER;
static int wps_timer_active = 0;
static int wps_timer_should_stop = 0;

// WPS定时器线程函数
void* wps_timer_thread_func(void* arg)
{
    int timeout_seconds = 120;
    int elapsed = 0;

    syslog(LOG_INFO, "WPS timer thread started, will timeout in %d seconds", timeout_seconds);

    while (elapsed < timeout_seconds)
    {
        sleep(1);
        elapsed++;

        pthread_mutex_lock(&wps_timer_mutex);
        if (wps_timer_should_stop)
        {
            pthread_mutex_unlock(&wps_timer_mutex);
            syslog(LOG_INFO, "WPS timer thread stopped by request");
            return NULL;
        }
        pthread_mutex_unlock(&wps_timer_mutex);
    }

    // 超时处理
    syslog(LOG_INFO, "WPS timer timeout (120s), writing WPS-TIMEOUT to file");
    os_execcmd("echo 'WPS-TIMEOUT' > /tmp/mesh_slave_wps_status");

    pthread_mutex_lock(&wps_timer_mutex);
    wps_timer_active = 0;
    pthread_mutex_unlock(&wps_timer_mutex);

    return NULL;
}

// 启动WPS定时器
int start_wps_timer(void)
{
    pthread_mutex_lock(&wps_timer_mutex);

    if (wps_timer_active)
    {
        pthread_mutex_unlock(&wps_timer_mutex);
        return 0; // 定时器已经在运行
    }

    wps_timer_should_stop = 0;
    wps_timer_active = 1;

    if (pthread_create(&wps_timer_thread, NULL, wps_timer_thread_func, NULL) != 0)
    {
        syslog(LOG_ERR, "Failed to create WPS timer thread");
        wps_timer_active = 0;
        pthread_mutex_unlock(&wps_timer_mutex);
        return -1;
    }

    pthread_detach(wps_timer_thread); // 分离线程，自动清理资源
    pthread_mutex_unlock(&wps_timer_mutex);

    syslog(LOG_INFO, "WPS timer started successfully");
    return 0;
}

// 停止WPS定时器
void stop_wps_timer(void)
{
    pthread_mutex_lock(&wps_timer_mutex);

    if (wps_timer_active)
    {
        wps_timer_should_stop = 1;
        wps_timer_active = 0;
        syslog(LOG_INFO, "WPS timer stop requested");
    }

    pthread_mutex_unlock(&wps_timer_mutex);
}

int os_execcmd(char *pc_command)
{
    int pid = 0;
    int status = 0;
    int *argv[] = {"sh", "-c", pc_command, NULL};

    if (pc_command == NULL)
    {
        return -1;
    }

    pid = fork();
    if (pid < 0)
    {
        return -1;
    }
    else if (pid == 0)
    {
        // 子进程执行分支
        execv("/bin/sh", argv);
        _exit(127);
    }

    /* wait for child process return */
    while (waitpid(pid, &status, 0) < 0)
    {
        if (errno != (unsigned int)EINTR)
        {
            return -1;
        }
    }

    return WIFEXITED(status) ? (0) : (-1);
}

int uci_set_led_blinking(char *led_name, int time)
{
    if (led_name == NULL)
        return -1;

    vsuci_del_section("system", led_name);
    vsuci_add_section("system", "led", led_name);
    vsuci_set("system", led_name, "name", led_name);
    vsuci_set("system", led_name, "sysfs", led_name);
    vsuci_set("system", led_name, "trigger", "timer");
    vsuci_set_int32(time, "system", led_name, "delayon");
    vsuci_set_int32(time, "system", led_name, "delayoff");

    os_execcmd("uci commit system");
    os_execcmd("/etc/init.d/led restart");
}

int uci_set_led_on_off(char *led_name, int on_off)
{
    if (led_name == NULL)
        return -1;

    vsuci_del_section("system", led_name);
    vsuci_add_section("system", "led", led_name);
    vsuci_set("system", led_name, "name", led_name);
    vsuci_set("system", led_name, "sysfs", led_name);
    if (on_off == LED_OFF)
    {
        vsuci_set("system", led_name, "trigger", "none");
        vsuci_set_int32(0, "system", led_name, "default");
    }
    else
    {
        vsuci_set("system", led_name, "trigger", "default-on");
    }

    os_execcmd("uci commit system");
    os_execcmd("/etc/init.d/led restart");
}

int uci_get_mesh_switch(void)
{   
    int vaule = 0;

    vaule = vsuci_get_int32("easymesh", "easymesh_default", "mesh_switch");
    return vaule;
}

int uci_get_mesh_mode(void)
{
    int vaule = 0;

    vaule = vsuci_get_int32("easymesh", "easymesh_default", "mesh_mode");
    return vaule;
}

int check_wps_status(const char *interface)
{
    FILE *fp;
    char command[256];
    char result[512];
    int status = WPS_TIME_OUT, ret = 0;

    snprintf(command, sizeof(command), "hostapd_cli -i %s wps_get_status", interface);

    fp = popen(command, "r");
    if (fp == NULL)
    {
        return WPS_TIME_OUT;
    }

    while (fgets(result, sizeof(result), fp) != NULL)
    {
        // 检查是否包含 "PBC Status: Active"
        if (strstr(result, "PBC Status: Active") != NULL)
        {
            status = WPS_ALIVE;
            break;
        }
        // 检查是否包含 "PBC Status:" 但不是 Active
        else if (strstr(result, "PBC Status:") != NULL)
        {
            status = WPS_TIME_OUT;
        }
    }

    ret = pclose(fp);
    if (ret == -1)
    {
        return WPS_TIME_OUT;
    }

    if (status == WPS_TIME_OUT)
    {
        syslog(LOG_INFO, "Failed to find PBC Status in command output\n");
        return WPS_TIME_OUT;
    }

    return status;
}

int check_wan_status(void)
{
    FILE *file;
    char content[16];
    int status = WAN_DOWN;

    file = fopen("/tmp/wan_status", "r");
    if (file == NULL)
    {
        return WAN_DOWN;
    }

    if (fgets(content, sizeof(content), file) == NULL)
    {
        fclose(file);
        return WAN_DOWN;
    }

    fclose(file);

    int value = atoi(content);

    if (value == 0)
    {
        status = WAN_DOWN;
    }
    else if (value == 1)
    {
        status = WAN_UP;
    }
    else
    {
        status = WAN_DOWN;
    }

    return status;
}

int check_mesh_slave_wps_status(void)
{
    FILE *fp;
    char result[128];
    int status = WPS_OTHER_STATUS;

    fp = fopen("/tmp/mesh_slave_wps_status", "r");
    if (fp == NULL)
    {
        return WPS_OTHER_STATUS;
    }

    if (fgets(result, sizeof(result), fp) != NULL)
    {
        result[strcspn(result, "\n")] = '\0';
        
        if (strcmp(result, "WPS-PBC-ACTIVE") == 0)
        {
            status = WPS_PBC_ACTIVE;
        }
        else if (strcmp(result, "WPS-TIMEOUT") == 0)
        {
            status = WPS_TIMEOUT;
        }
        else if (strcmp(result, "WPS-SUCCESS") == 0)
        {
            status = WPS_SUCCESS;
        }
        else if (strcmp(result, "WPS-FAIL") == 0)
        {
            status = WPS_FAIL;
        }
        else
        {
            status = WPS_OTHER_STATUS;
        }
    }
    else
    {
        syslog(LOG_INFO, "Failed to read from /tmp/mesh_slave_wps_status\n");
        status = WPS_OTHER_STATUS;
    }

    fclose(fp);
    return status;
}

int check_mesh_slave_ip_status(void)
{
    FILE *file;
    char content[32];
    int status = MESH_SLAVE_IP_DISCONNECTED;

    file = fopen("/tmp/mesh_status", "r");
    if (file == NULL)
    {
        return MESH_SLAVE_IP_DISCONNECTED;
    }

    if (fgets(content, sizeof(content), file) == NULL)
    {
        fclose(file);
        return MESH_SLAVE_IP_DISCONNECTED;
    }

    fclose(file);

    content[strcspn(content, "\n")] = '\0';

    if (strcmp(content, "connected") == 0)
    {
        status = MESH_SLAVE_IP_CONNECTED;
    }
    else
    {
        status = MESH_SLAVE_IP_DISCONNECTED;
    }

    return status;
}

int check_mesh_slave_sta_info(const char *proc_name)
{
    FILE *file;
    char path[256];
    char line[256];
    int user_nums = -1;

    snprintf(path, sizeof(path), "/proc/%s/sta_info", proc_name);

    file = fopen(path, "r");
    if (file == NULL)
    {
        return STA_INFO_DISCONNECTED;
    }

    while (fgets(line, sizeof(line), file))
    {
        // 查找包含"Total user nums:"的行
        if (strstr(line, "Total user nums:"))
        {
            // 提取数字
            if (sscanf(line, "Total user nums: %d", &user_nums) == 1)
            {
                break;
            }
        }
    }

    fclose(file);

    if (user_nums > 0)
    {
        return STA_INFO_CONNECTED;
    }
    else
    {
        return STA_INFO_DISCONNECTED;
    }
}

int check_factory_reset_status(void)
{
    FILE *file;
    char content[16];
    int status = FACTORY_NOT_RESET;

    file = fopen("/tmp/factory_reset", "r");
    if (file == NULL)
    {
        return FACTORY_NOT_RESET;
    }

    if (fgets(content, sizeof(content), file) == NULL)
    {
        fclose(file);
        return FACTORY_NOT_RESET;
    }

    fclose(file);

    int value = atoi(content);

    if (value == 0)
    {
        status = FACTORY_NOT_RESET;
    }
    else if (value == 1)
    {
        status = FACTORY_RESET;
    }
    else
    {
        status = FACTORY_NOT_RESET;
    }

    return status;
}

int get_board_name(char *name, size_t max_len)
{
    FILE *fp;
    size_t bytes_read;
    int ret = -1;

    if (name == NULL || max_len == 0)
    {
        return -1;
    }

    fp = fopen("/sys/firmware/devicetree/base/board_name_alias", "r");
    if (fp == NULL)
    {
        perror("Failed to open board_name_alias");
        return -1;
    }

    bytes_read = fread(name, 1, max_len - 1, fp);
    if (bytes_read == 0)
    {
        if (ferror(fp))
        {
            perror("Error reading from file");
        }
        else
        {
            fprintf(stderr, "File is empty\n");
        }
        fclose(fp);
        return -1;
    }

    // 确保字符串以null结尾
    name[bytes_read] = '\0';

    // 移除可能的换行符（如果有的话）
    if (bytes_read > 0 && name[bytes_read - 1] == '\n')
    {
        name[bytes_read - 1] = '\0';
        bytes_read--;
    }

    fclose(fp);

    if (bytes_read > 0)
    {
        ret = 0;
    }

    return ret;
}

int main(int argc, char *argv[])
{
    char board_name[32] = {0};
    int mesh_switch = MESH_DISABLE, mesh_mode = MESH_MASTER_MODE;
    int last_led_status = LED_SLOW_BLINKING, set_led_status = LED_SLOW_BLINKING;
    int now_2g_status = WPS_TIME_OUT, now_5g_status = WPS_TIME_OUT;
    int last_2g_status = WPS_TIME_OUT, last_5g_status = WPS_TIME_OUT;
    int led_wps_status = WPS_TIME_OUT;
    int mesh_slave_wps_status = WPS_OTHER_STATUS;
    static int last_mesh_slave_wps_status = WPS_OTHER_STATUS;

    //get device name
    get_board_name(board_name, sizeof(board_name));

    if (strcmp(board_name, "hg5012ac") == 0 
        || strcmp(board_name, "hg5013_4g_h2") == 0 
        || strcmp(board_name, "hg5013_4g_h3") == 0 )
    {
        uci_set_led_blinking("led_internet", 1000);//set to slow blinking when system startup
    }
    else if (strcmp(board_name, "hg5013_4g") == 0 )
    {
        uci_set_led_blinking("led_power", 1000);//set to slow blinking when system startup
    }
    
    while (1)
    {
        mesh_switch = uci_get_mesh_switch();
        mesh_mode = uci_get_mesh_mode();
        mesh_slave_wps_status = check_mesh_slave_wps_status();

        // WPS定时器逻辑处理
        if (mesh_slave_wps_status == WPS_PBC_ACTIVE)
        {
            // 如果状态刚变为WPS_PBC_ACTIVE，启动定时器
            if (last_mesh_slave_wps_status != WPS_PBC_ACTIVE)
            {
                start_wps_timer();
            }
        }
        else
        {
            // 如果状态不是WPS_PBC_ACTIVE，停止定时器
            if (last_mesh_slave_wps_status == WPS_PBC_ACTIVE)
            {
                stop_wps_timer();
            }
        }
        last_mesh_slave_wps_status = mesh_slave_wps_status;

        if (check_wps_status("default_radio0") == WPS_ALIVE)
            now_2g_status = WPS_ALIVE;
        else
            now_2g_status = WPS_TIME_OUT;
        if (check_wps_status("default_radio1") == WPS_ALIVE)
            now_5g_status = WPS_ALIVE;
        else
            now_5g_status = WPS_TIME_OUT;

        //from WPS_ALIVE change to WPS_TIME_OUT
        if ((now_2g_status != last_2g_status && last_2g_status == WPS_ALIVE) 
            || (now_5g_status != last_5g_status && last_5g_status == WPS_ALIVE))
        {
            led_wps_status = WPS_TIME_OUT;
        }
        //from WPS_TIME_OUT change to WPS_ALIVE
        if ((now_2g_status != last_2g_status && last_2g_status == WPS_TIME_OUT) 
            || (now_5g_status != last_5g_status && last_5g_status == WPS_TIME_OUT))
        {
            led_wps_status = WPS_ALIVE;
        }

        //judge led status
        if (mesh_switch == MESH_ENABLE)//mesh enable
        {
            if (mesh_mode == MESH_MASTER_MODE)//mesh master mode
            {
                if (led_wps_status == WPS_ALIVE)//wps alive
                {
                    set_led_status = LED_FAST_BLINKING;
                }
                else
                {
                    if (check_wan_status() == WAN_UP)//wan up
                    {
                        set_led_status = LED_KEEP_ON;
                    }
                    else//wan down 
                    {
                        set_led_status = LED_SLOW_BLINKING;
                    }
                }
            }
            else if (mesh_mode == MESH_SLAVE_MODE)//mesh slave mode
            {
                if (mesh_slave_wps_status == WPS_PBC_ACTIVE)//wps alive
                {
                    set_led_status = LED_FAST_BLINKING;
                }
                else
                {
                    if (check_mesh_slave_sta_info("em_bh_2g_sta") == STA_INFO_CONNECTED
                        || check_mesh_slave_sta_info("em_bh_5g_sta") == STA_INFO_CONNECTED
                        || check_mesh_slave_ip_status() == MESH_SLAVE_IP_CONNECTED)//sta connect or have get ip
                    {
                        set_led_status = LED_KEEP_ON;
                    }
                    else//disconnect
                    {
                        set_led_status = LED_SLOW_BLINKING;
                    }
                }
            }
        }
        else//mesh disable  
        {
            if (led_wps_status == WPS_ALIVE)//wps alive
            {
                set_led_status = LED_FAST_BLINKING;
            }
            else
            {
                if (check_wan_status() == WAN_UP)//wan up
                {
                    set_led_status = LED_KEEP_ON;
                }
                else//wan down
                {
                    set_led_status = LED_SLOW_BLINKING;
                }
            }
        }

        if (check_factory_reset_status() == FACTORY_RESET)
        {
            set_led_status = LED_KEEP_OFF;
        }

        //take effect led status
        if (strcmp(board_name, "hg5012ac") == 0 
        || strcmp(board_name, "hg5013_4g_h2") == 0 
        || strcmp(board_name, "hg5013_4g_h3") == 0 )
        {
            if (last_led_status != set_led_status)//led status update
            {
                switch (set_led_status)
                {
                    case LED_SLOW_BLINKING:
                        syslog(LOG_INFO, "set led LED_SLOW_BLINKING");
                        uci_set_led_blinking("led_internet", 1000);
                        break;
                    case LED_FAST_BLINKING:
                        syslog(LOG_INFO, "set led LED_FAST_BLINKING");
                        uci_set_led_blinking("led_internet", 50);
                        break;
                    case LED_KEEP_ON:
                        syslog(LOG_INFO, "set led LED_KEEP_ON");
                        uci_set_led_on_off("led_internet", LED_ON);
                        break;
                    case LED_KEEP_OFF:
                        syslog(LOG_INFO, "set led LED_KEEP_OFF");
                        uci_set_led_on_off("led_power", LED_OFF);
                        uci_set_led_on_off("led_internet", LED_OFF);
                        break;
                    default:
                        syslog(LOG_INFO, "unknown led status, set led LED_SLOW_BLINKING");
                        uci_set_led_blinking("led_internet", 1000);
                        break;
                }
            }
        }
        else if (strcmp(board_name, "hg5013_4g") == 0)
        {
            if (last_led_status != set_led_status)//led status update
            {
                switch (set_led_status)
                {
                    case LED_SLOW_BLINKING:
                        syslog(LOG_INFO, "set led LED_SLOW_BLINKING");
                        uci_set_led_blinking("led_power", 1000);
                        break;
                    case LED_FAST_BLINKING:
                        syslog(LOG_INFO, "set led LED_FAST_BLINKING");
                        uci_set_led_blinking("led_power", 50);
                        break;
                    case LED_KEEP_ON:
                        syslog(LOG_INFO, "set led LED_KEEP_ON");
                        uci_set_led_on_off("led_power", LED_ON);
                        break;
                    case LED_KEEP_OFF:
                        syslog(LOG_INFO, "set led LED_KEEP_OFF");
                        uci_set_led_on_off("led_power", LED_OFF);
                        break;
                    default:
                        syslog(LOG_INFO, "unknown led status, set led LED_SLOW_BLINKING");
                        uci_set_led_blinking("led_power", 1000);
                        break;
                }
            }
        }

        last_led_status = set_led_status;
        last_2g_status = now_2g_status;
        last_5g_status = now_5g_status;
        sleep(1);
    }

    return 0;
}
