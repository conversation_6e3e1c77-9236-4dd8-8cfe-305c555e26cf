#ifndef __LED_UTIL_H__
#define __LED_UTIL_H__
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <syslog.h>
#include <errno.h>
#include <signal.h>
#include <uci.h>

int os_execcmd(char *pc_command);
int uci_set_led_blinking(char *led_name, int time);
int uci_set_led_on_off(char *led_name, int on_off);
int uci_get_mesh_switch(void);
int uci_get_mesh_mode(void);

enum led_status{
    LED_SLOW_BLINKING     = 0,
    LED_FAST_BLINKING     = 1,
    LED_KEEP_ON           = 2,
    LED_KEEP_OFF          = 3,
};

enum led_on_off{
    LED_OFF     = 0,
    LED_ON      = 1,
};

enum mesh_mode{
    MESH_MASTER_MODE    = 0,
    MESH_SLAVE_MODE     = 1,
};

enum mesh_switch{
    MESH_DISABLE    = 0,
    MESH_ENABLE     = 1,
};

enum wps_status{
    WPS_TIME_OUT    = 0,
    WPS_ALIVE       = 1,
};

enum wan_status{
    WAN_DOWN        = 0,
    WAN_UP          = 1,
};

enum mesh_slave_status {
    MESH_SLAVE_IP_DISCONNECTED  = 0,
    MESH_SLAVE_IP_CONNECTED     = 1,
};

enum factory_reset {
    FACTORY_NOT_RESET   = 0,
    FACTORY_RESET       = 1,
};

enum mesh_slave_wps_status {
    WPS_OTHER_STATUS   = 0,
    WPS_PBC_ACTIVE     = 1,
    WPS_TIMEOUT        = 2,
    WPS_SUCCESS        = 3,
    WPS_FAIL           = 4,
};

enum mesh_slave_sta_info {
    STA_INFO_DISCONNECTED  = 0,
    STA_INFO_CONNECTED     = 1,
};

#endif //__LED_UTIL_H__
